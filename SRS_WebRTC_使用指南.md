# SRS WebRTC局域网推流系统使用指南

## 系统概述

本系统基于SRS 5.0搭建，专门为局域网环境优化，支持WebRTC推流和多种格式播放。

**服务器信息:**
- 内网IP: **************
- 无公网IP，仅局域网使用
- 防火墙已完全开放

## 快速开始

### 1. 编译SRS（首次使用）

```bash
# 进入SRS源码目录
cd srs/trunk

# 配置编译选项（启用WebRTC支持）
./configure --with-webrtc --with-rtc --with-ssl --with-http-api --with-http-server

# 编译（约5-15分钟）
make

# 验证编译结果
./objs/srs -v
```

### 2. 启动SRS服务器

```bash
# 在项目根目录执行
./start_srs_webrtc.sh
```

启动成功后会显示：
```
==========================================
SRS WebRTC局域网推流服务器启动脚本
服务器IP: **************
==========================================

服务端口:
  - RTMP推流: 1935
  - WebRTC: 8000 (UDP)
  - HTTP API: 1985
  - HTTP服务器: 8080

前端推流页面: http://**************:8080/webrtc_publisher.html
SRS管理页面: http://**************:8080/
```

### 3. 使用推流界面

在局域网内任意电脑的浏览器中访问：
```
http://**************:8080/webrtc_publisher.html
```

## 详细功能说明

### 推流界面功能

1. **推流地址配置**
   - 默认地址: `webrtc://**************:8000/live/livestream`
   - 可修改流名称，地址会自动更新

2. **视频质量选择**
   - 320x240 (低质量) - 适合网络较差环境
   - 640x480 (标准质量) - 推荐设置
   - 1280x720 (高质量) - 需要较好的网络和设备性能

3. **推流控制**
   - 开始推流: 获取摄像头权限并开始推流
   - 停止推流: 停止推流并释放资源

4. **状态监控**
   - 连接状态: 显示当前推流状态
   - 会话ID: WebRTC会话标识
   - 编解码器: 显示音视频编码格式
   - 推流时长: 实时显示推流持续时间

5. **播放地址**
   - WebRTC播放: `webrtc://**************:8000/live/流名称`
   - HTTP-FLV播放: `http://**************:8080/live/流名称.flv`
   - HLS播放: `http://**************:8080/live/流名称.m3u8`
   - RTMP播放: `rtmp://**************:1935/live/流名称`

### 配置文件说明

配置文件位置: `srs/trunk/conf/webrtc_lan.conf`

**关键配置项:**
```nginx
# WebRTC服务器配置
rtc_server {
    enabled         on;
    listen          8000;           # WebRTC UDP端口
    candidate       **************; # 固定内网IP
    tcp {
        enabled     on;             # 启用TCP支持
        listen      8000;
    }
    protocol        all;            # 支持UDP+TCP
}

# 虚拟主机配置
vhost __defaultVhost__ {
    rtc {
        enabled         on;
        rtmp_to_rtc     on;         # RTMP转WebRTC
        rtc_to_rtmp     on;         # WebRTC转RTMP
        nack            on;         # 启用重传
        twcc            on;         # 启用拥塞控制
    }
}
```

## 测试验证

### 1. 服务器状态检查

```bash
# 检查SRS进程
ps aux | grep srs

# 检查端口监听
netstat -tuln | grep -E "(1935|8000|1985|8080)"

# 检查API状态
curl http://**************:1985/api/v1/summaries
```

### 2. 推流测试步骤

1. **准备工作**
   - 确保SRS服务器已启动
   - 准备一台有摄像头的电脑作为推流端
   - 准备另一台电脑作为播放端（可选）

2. **开始推流**
   - 在推流端浏览器访问: `http://**************:8080/webrtc_publisher.html`
   - 授权摄像头和麦克风权限
   - 点击"开始推流"按钮
   - 观察状态显示是否为"推流中"

3. **验证推流**
   ```bash
   # 检查当前推流
   curl http://**************:1985/api/v1/streams
   
   # 应该返回包含流信息的JSON数据
   ```

4. **播放测试**
   - 在播放端浏览器访问SRS播放页面: `http://**************:8080/players/rtc_player.html`
   - 输入播放地址: `webrtc://**************:8000/live/livestream`
   - 点击播放，应该能看到推流画面

### 3. 多种播放方式测试

1. **WebRTC播放** (低延迟)
   ```
   webrtc://**************:8000/live/livestream
   ```

2. **HTTP-FLV播放** (兼容性好)
   ```
   http://**************:8080/live/livestream.flv
   ```

3. **HLS播放** (移动端友好)
   ```
   http://**************:8080/live/livestream.m3u8
   ```

4. **RTMP播放** (传统播放器)
   ```
   rtmp://**************:1935/live/livestream
   ```

## 常见问题解决

### 1. 推流失败

**问题**: 点击开始推流后提示失败

**解决方案**:
- 检查浏览器是否支持WebRTC (Chrome、Firefox、Safari)
- 确认已授权摄像头和麦克风权限
- 检查SRS服务器是否正常运行
- 确认网络连接正常

### 2. 无法访问推流页面

**问题**: 浏览器无法打开推流页面

**解决方案**:
- 确认SRS服务器已启动
- 检查8080端口是否正常监听
- 确认IP地址**************可访问
- 尝试直接访问: `http://**************:8080/`

### 3. 推流成功但无法播放

**问题**: 推流状态显示成功，但播放端看不到画面

**解决方案**:
- 检查流名称是否一致
- 确认播放地址格式正确
- 尝试不同的播放方式（WebRTC、HTTP-FLV等）
- 检查SRS日志输出

### 4. 网络连接问题

**问题**: WebRTC连接失败

**解决方案**:
- 确认UDP 8000端口可访问
- 检查candidate配置是否正确
- 尝试启用TCP传输模式
- 检查网络NAT配置

## 性能优化建议

### 1. 网络优化
- 确保局域网带宽充足
- 使用有线网络连接（推荐）
- 避免网络拥塞时段

### 2. 设备优化
- 使用性能较好的摄像头
- 确保CPU性能充足
- 关闭不必要的后台程序

### 3. 配置优化
- 根据网络情况调整视频质量
- 启用硬件加速（如果支持）
- 合理设置GOP缓存大小

## 技术支持

如遇到问题，可以：

1. 查看SRS服务器日志输出
2. 检查浏览器开发者工具的控制台错误
3. 使用API接口检查服务器状态
4. 参考SRS官方文档: https://ossrs.net/

---

**注意**: 本系统仅适用于局域网环境，如需公网访问请配置相应的网络设置和安全策略。
