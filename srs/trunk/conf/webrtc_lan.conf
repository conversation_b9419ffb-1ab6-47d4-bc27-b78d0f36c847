# SRS WebRTC局域网推流配置文件
# 适用于内网环境，支持WebRTC推流和播放
# 服务器IP: **************

# 基础服务配置
listen              1935;
max_connections     1000;
daemon              off;
srs_log_tank        console;
srs_log_level       info;

# HTTP服务器配置 - 提供Web界面和静态文件服务
http_server {
    enabled         on;
    listen          8080;
    dir             ./objs/nginx/html;
    crossdomain     on;

    # HTTPS配置 - 解决WebRTC HTTPS要求
    https {
        enabled     on;
        listen      8443;
        key         ./conf/server.key;
        cert        ./conf/server.crt;
    }
}

# HTTP API配置 - 提供REST API接口
http_api {
    enabled         on;
    listen          1985;
    crossdomain     on;
}

# WebRTC服务器配置 - 核心配置
rtc_server {
    enabled         on;
    listen          8000;
    candidate       **************;
}

# 默认虚拟主机配置
vhost __defaultVhost__ {
    # WebRTC配置
    rtc {
        enabled         on;
        rtmp_to_rtc     on;
        rtc_to_rtmp     on;
    }

    # HTTP重混配置 - 支持HTTP-FLV播放
    http_remux {
        enabled         on;
        mount           [vhost]/[app]/[stream].flv;
    }

    # HLS配置 - 支持HLS播放
    hls {
        enabled         on;
        hls_fragment    10;
        hls_window      60;
        hls_path        ./objs/nginx/html;
        hls_m3u8_file   [app]/[stream].m3u8;
        hls_ts_file     [app]/[stream]-[seq].ts;
    }
}
