# SRS WebRTC局域网推流配置文件
# 适用于内网环境，支持WebRTC推流和播放
# 服务器IP: **************

# 基础服务配置
listen              1935;
max_connections     1000;
daemon              off;
srs_log_tank        console;
srs_log_level       info;

# HTTP服务器配置 - 提供Web界面和静态文件服务
http_server {
    enabled         on;
    listen          8080;
    dir             ./objs/nginx/html;
    # 支持跨域访问
    crossdomain     on;
}

# HTTP API配置 - 提供REST API接口
http_api {
    enabled         on;
    listen          1985;
    # 支持跨域访问，方便前端调用
    crossdomain     on;
}

# 统计信息配置
stats {
    network         0;
    disk            sda vda xvda xvdb;
}

# WebRTC服务器配置 - 核心配置
rtc_server {
    enabled         on;
    listen          8000;           # WebRTC UDP端口
    
    # 候选地址配置 - 设置为内网IP
    candidate       **************;
    
    # 支持TCP传输（提高连接成功率）
    tcp {
        enabled     on;
        listen      8000;
    }
    
    # 协议支持：UDP和TCP
    protocol        all;
    
    # 性能优化配置
    reuseport       1;
    merge_nalus     off;
    
    # 自动检测网络IP（禁用，使用固定IP）
    use_auto_detect_network_ip off;
    
    # IP族过滤
    ip_family       ipv4;
    
    # API作为候选地址
    api_as_candidates on;
    resolve_api_domain on;
    keep_api_domain off;
    
    # 加密配置
    ecdsa           on;
    encrypt         on;
}

# 默认虚拟主机配置
vhost __defaultVhost__ {
    # WebRTC配置
    rtc {
        enabled         on;
        
        # 启用RTMP到WebRTC的转换
        rtmp_to_rtc     on;
        # 启用WebRTC到RTMP的转换
        rtc_to_rtmp     on;
        
        # 网络优化配置
        nack            on;         # 启用NACK重传
        twcc            on;         # 启用传输拥塞控制
        nack_no_copy    on;         # 优化NACK性能
        
        # 会话超时配置
        stun_timeout    30;
        stun_strict_check off;
        
        # DTLS配置
        dtls_role       passive;
        dtls_version    auto;
        
        # 保持B帧（直播场景可关闭）
        keep_bframe     off;
        keep_avc_nalu_sei on;
        
        # 音频编码配置
        opus_bitrate    48000;
        aac_bitrate     48000;
        
        # PLI间隔配置
        pli_for_rtmp    6.0;
    }
    
    # HTTP重混配置 - 支持HTTP-FLV播放
    http_remux {
        enabled         on;
        mount           [vhost]/[app]/[stream].flv;
    }
    
    # HLS配置 - 支持HLS播放
    hls {
        enabled         on;
        hls_fragment    10;
        hls_window      60;
        hls_path        ./objs/nginx/html;
        hls_m3u8_file   [app]/[stream].m3u8;
        hls_ts_file     [app]/[stream]-[seq].ts;
    }
    
    # 播放优化配置
    play {
        # GOP缓存优化
        gop_cache       on;
        gop_cache_max_frames 2500;
        
        # 延迟优化
        mw_latency      0;
        mw_msgs         0;
        
        # 队列长度
        queue_length    10;
    }
    
    # 发布优化配置
    publish {
        # 解析SPS
        parse_sps       on;
        # 第一个GOP缓存
        firstgop        on;
        # 正常超时
        normal_timeout  5000;
        # 关闭MR
        mr              off;
    }
    
    # 低延迟模式（WebRTC推荐）
    min_latency     on;
    
    # TCP无延迟
    tcp_nodelay     on;
}
