#!/bin/bash

# SRS WebRTC局域网推流服务器启动脚本
# 服务器IP: **************

echo "=========================================="
echo "SRS WebRTC局域网推流服务器启动脚本"
echo "服务器IP: **************"
echo "=========================================="

# 检查SRS是否已编译
if [ ! -f "srs/trunk/objs/srs" ]; then
    echo "错误: SRS未编译，请先编译SRS"
    echo "编译命令:"
    echo "  cd srs/trunk"
    echo "  ./configure --with-webrtc --with-rtc --with-ssl --with-http-api --with-http-server"
    echo "  make"
    exit 1
fi

# 检查配置文件是否存在
if [ ! -f "srs/trunk/conf/webrtc_lan.conf" ]; then
    echo "错误: 配置文件不存在: srs/trunk/conf/webrtc_lan.conf"
    exit 1
fi

# 检查端口是否被占用
check_port() {
    local port=$1
    local service=$2
    if netstat -tuln | grep -q ":$port "; then
        echo "警告: 端口 $port ($service) 已被占用"
        echo "请检查是否有其他SRS实例正在运行"
        return 1
    fi
    return 0
}

echo "检查端口占用情况..."
check_port 1935 "RTMP"
check_port 8000 "WebRTC"
check_port 1985 "HTTP API"
check_port 8080 "HTTP Server"

# 创建必要的目录
echo "创建必要的目录..."
mkdir -p srs/trunk/objs/nginx/html/live

# 复制前端文件到SRS的web目录
echo "复制前端文件..."
if [ -f "web/webrtc_publisher.html" ]; then
    cp web/webrtc_publisher.html srs/trunk/objs/nginx/html/
    echo "前端推流页面已复制到: srs/trunk/objs/nginx/html/webrtc_publisher.html"
else
    echo "警告: 前端文件不存在: web/webrtc_publisher.html"
fi

# 设置环境变量
export CANDIDATE=**************

# 进入SRS目录
cd srs/trunk

echo ""
echo "启动SRS服务器..."
echo "配置文件: conf/webrtc_lan.conf"
echo "服务器IP: $CANDIDATE"
echo ""
echo "服务端口:"
echo "  - RTMP推流: 1935"
echo "  - WebRTC: 8000 (UDP)"
echo "  - HTTP API: 1985"
echo "  - HTTP服务器: 8080"
echo ""
echo "前端推流页面: http://**************:8080/webrtc_publisher.html"
echo "SRS管理页面: http://**************:8080/"
echo ""
echo "按 Ctrl+C 停止服务器"
echo "=========================================="

# 启动SRS服务器
./objs/srs -c conf/webrtc_lan.conf
