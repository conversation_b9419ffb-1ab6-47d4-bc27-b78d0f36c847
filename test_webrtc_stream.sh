#!/bin/bash

# WebRTC推流测试脚本
# 用于验证SRS WebRTC推流功能

echo "=========================================="
echo "WebRTC推流功能测试"
echo "服务器IP: **************"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查函数
check_ok() {
    echo -e "${GREEN}✓${NC} $1"
}

check_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

check_error() {
    echo -e "${RED}✗${NC} $1"
}

check_info() {
    echo -e "${BLUE}ℹ${NC} $1"
}

# 1. 检查SRS服务状态
echo "1. 检查SRS服务状态..."
if pgrep -f "objs/srs" > /dev/null; then
    SRS_PID=$(pgrep -f "objs/srs")
    check_ok "SRS服务正在运行 (PID: $SRS_PID)"
else
    check_error "SRS服务未运行，请先启动服务"
    echo "   启动命令: ./start_srs_webrtc.sh"
    exit 1
fi

# 2. 测试HTTP API
echo ""
echo "2. 测试HTTP API..."
if command -v curl >/dev/null 2>&1; then
    API_RESPONSE=$(curl -s -w "%{http_code}" http://**************:1985/api/v1/summaries -o /dev/null)
    if [ "$API_RESPONSE" = "200" ]; then
        check_ok "HTTP API接口正常 (HTTP $API_RESPONSE)"
        
        # 获取服务器信息
        SERVER_INFO=$(curl -s http://**************:1985/api/v1/summaries | grep -o '"version":"[^"]*"' | cut -d'"' -f4)
        if [ -n "$SERVER_INFO" ]; then
            check_info "SRS版本: $SERVER_INFO"
        fi
    else
        check_error "HTTP API接口异常 (HTTP $API_RESPONSE)"
    fi
else
    check_warning "curl命令不可用，跳过API测试"
fi

# 3. 测试Web服务器
echo ""
echo "3. 测试Web服务器..."
if command -v curl >/dev/null 2>&1; then
    WEB_RESPONSE=$(curl -s -w "%{http_code}" http://**************:8080/ -o /dev/null)
    if [ "$WEB_RESPONSE" = "200" ]; then
        check_ok "HTTP Web服务器正常 (HTTP $WEB_RESPONSE)"
    else
        check_error "HTTP Web服务器异常 (HTTP $WEB_RESPONSE)"
    fi
    
    # 检查推流页面
    PUBLISHER_RESPONSE=$(curl -s -w "%{http_code}" http://**************:8080/webrtc_publisher.html -o /dev/null)
    if [ "$PUBLISHER_RESPONSE" = "200" ]; then
        check_ok "推流页面可访问 (HTTP $PUBLISHER_RESPONSE)"
    else
        check_error "推流页面不可访问 (HTTP $PUBLISHER_RESPONSE)"
    fi
else
    check_warning "curl命令不可用，跳过Web服务器测试"
fi

# 4. 检查当前推流状态
echo ""
echo "4. 检查当前推流状态..."
if command -v curl >/dev/null 2>&1; then
    STREAMS=$(curl -s http://**************:1985/api/v1/streams)
    STREAM_COUNT=$(echo "$STREAMS" | grep -o '"stream"' | wc -l)
    
    if [ "$STREAM_COUNT" -gt 0 ]; then
        check_ok "检测到 $STREAM_COUNT 个活跃推流"
        echo "$STREAMS" | grep -o '"url":"[^"]*"' | sed 's/"url":"//g' | sed 's/"//g' | while read url; do
            check_info "推流地址: $url"
        done
    else
        check_info "当前没有活跃的推流"
    fi
else
    check_warning "curl命令不可用，跳过推流状态检查"
fi

# 5. 网络连通性测试
echo ""
echo "5. 网络连通性测试..."
if ping -c 1 ************** >/dev/null 2>&1; then
    check_ok "服务器IP ************** 可访问"
else
    check_error "服务器IP ************** 不可访问"
fi

# 6. 显示访问信息
echo ""
echo "=========================================="
echo "系统访问信息"
echo "=========================================="
echo -e "${BLUE}推流页面:${NC}"
echo "  http://**************:8080/webrtc_publisher.html"
echo ""
echo -e "${BLUE}管理页面:${NC}"
echo "  http://**************:8080/"
echo ""
echo -e "${BLUE}推流地址:${NC}"
echo "  webrtc://**************:8000/live/livestream"
echo ""
echo -e "${BLUE}播放地址:${NC}"
echo "  WebRTC:   webrtc://**************:8000/live/livestream"
echo "  HTTP-FLV: http://**************:8080/live/livestream.flv"
echo "  HLS:      http://**************:8080/live/livestream.m3u8"
echo "  RTMP:     rtmp://**************:1935/live/livestream"

# 7. 使用说明
echo ""
echo "=========================================="
echo "使用说明"
echo "=========================================="
echo "1. 在局域网内任意电脑浏览器中访问推流页面"
echo "2. 授权摄像头和麦克风权限"
echo "3. 点击'开始推流'按钮"
echo "4. 观察推流状态显示"
echo "5. 使用播放地址在其他设备上观看"
echo ""
echo "如遇问题，请查看: SRS_WebRTC_使用指南.md"
