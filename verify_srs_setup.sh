#!/bin/bash

# SRS WebRTC系统验证脚本
# 检查系统配置和服务状态

echo "=========================================="
echo "SRS WebRTC系统验证脚本"
echo "服务器IP: **************"
echo "=========================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 检查函数
check_ok() {
    echo -e "${GREEN}✓${NC} $1"
}

check_warning() {
    echo -e "${YELLOW}⚠${NC} $1"
}

check_error() {
    echo -e "${RED}✗${NC} $1"
}

# 1. 检查SRS编译状态
echo "1. 检查SRS编译状态..."
if [ -f "srs/trunk/objs/srs" ]; then
    check_ok "SRS已编译"
    SRS_VERSION=$(srs/trunk/objs/srs -v 2>/dev/null | head -1)
    echo "   版本: $SRS_VERSION"
else
    check_error "SRS未编译，请先运行编译命令"
    echo "   编译命令: cd srs/trunk && ./configure --with-webrtc --with-rtc --with-ssl --with-http-api --with-http-server && make"
    exit 1
fi

# 2. 检查配置文件
echo ""
echo "2. 检查配置文件..."
if [ -f "srs/trunk/conf/webrtc_lan.conf" ]; then
    check_ok "配置文件存在: srs/trunk/conf/webrtc_lan.conf"
    
    # 检查关键配置
    if grep -q "candidate.***************" srs/trunk/conf/webrtc_lan.conf; then
        check_ok "CANDIDATE配置正确"
    else
        check_warning "CANDIDATE配置可能有问题"
    fi
    
    if grep -q "rtc_server" srs/trunk/conf/webrtc_lan.conf; then
        check_ok "WebRTC服务器配置存在"
    else
        check_error "WebRTC服务器配置缺失"
    fi
else
    check_error "配置文件不存在: srs/trunk/conf/webrtc_lan.conf"
fi

# 3. 检查前端文件
echo ""
echo "3. 检查前端文件..."
if [ -f "web/webrtc_publisher.html" ]; then
    check_ok "前端推流页面存在: web/webrtc_publisher.html"
else
    check_error "前端推流页面不存在: web/webrtc_publisher.html"
fi

# 4. 检查SRS进程状态
echo ""
echo "4. 检查SRS服务状态..."
SRS_PID=$(pgrep -f "objs/srs")
if [ -n "$SRS_PID" ]; then
    check_ok "SRS服务正在运行 (PID: $SRS_PID)"
    
    # 检查端口监听
    echo ""
    echo "5. 检查端口监听状态..."
    
    # 检查各个端口
    if netstat -tuln 2>/dev/null | grep -q ":1935 "; then
        check_ok "RTMP端口 1935 正在监听"
    else
        check_error "RTMP端口 1935 未监听"
    fi
    
    if netstat -tuln 2>/dev/null | grep -q ":8000 "; then
        check_ok "WebRTC端口 8000 正在监听"
    else
        check_error "WebRTC端口 8000 未监听"
    fi
    
    if netstat -tuln 2>/dev/null | grep -q ":1985 "; then
        check_ok "HTTP API端口 1985 正在监听"
    else
        check_error "HTTP API端口 1985 未监听"
    fi
    
    if netstat -tuln 2>/dev/null | grep -q ":8080 "; then
        check_ok "HTTP服务器端口 8080 正在监听"
    else
        check_error "HTTP服务器端口 8080 未监听"
    fi
    
    # 6. 测试API接口
    echo ""
    echo "6. 测试API接口..."
    if command -v curl >/dev/null 2>&1; then
        API_RESPONSE=$(curl -s -w "%{http_code}" http://**************:1985/api/v1/summaries -o /dev/null)
        if [ "$API_RESPONSE" = "200" ]; then
            check_ok "HTTP API接口响应正常"
        else
            check_error "HTTP API接口响应异常 (HTTP $API_RESPONSE)"
        fi
    else
        check_warning "curl命令不可用，跳过API测试"
    fi
    
    # 7. 测试Web服务器
    echo ""
    echo "7. 测试Web服务器..."
    if command -v curl >/dev/null 2>&1; then
        WEB_RESPONSE=$(curl -s -w "%{http_code}" http://**************:8080/ -o /dev/null)
        if [ "$WEB_RESPONSE" = "200" ]; then
            check_ok "HTTP Web服务器响应正常"
        else
            check_error "HTTP Web服务器响应异常 (HTTP $WEB_RESPONSE)"
        fi
    else
        check_warning "curl命令不可用，跳过Web服务器测试"
    fi
    
else
    check_error "SRS服务未运行"
    echo "   启动命令: ./start_srs_webrtc.sh"
fi

# 8. 网络连通性测试
echo ""
echo "8. 网络连通性测试..."
if ping -c 1 ************** >/dev/null 2>&1; then
    check_ok "服务器IP ************** 可访问"
else
    check_error "服务器IP ************** 不可访问"
fi

# 9. 系统资源检查
echo ""
echo "9. 系统资源检查..."
MEMORY_USAGE=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
CPU_LOAD=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')

echo "   内存使用率: ${MEMORY_USAGE}%"
echo "   CPU负载: ${CPU_LOAD}"

if (( $(echo "$MEMORY_USAGE < 80" | bc -l) )); then
    check_ok "内存使用率正常"
else
    check_warning "内存使用率较高: ${MEMORY_USAGE}%"
fi

# 10. 总结和建议
echo ""
echo "=========================================="
echo "验证完成 - 系统状态总结"
echo "=========================================="

if [ -n "$SRS_PID" ]; then
    echo -e "${GREEN}✓ SRS服务运行正常${NC}"
    echo ""
    echo "访问地址:"
    echo "  推流页面: http://**************:8080/webrtc_publisher.html"
    echo "  管理页面: http://**************:8080/"
    echo "  API接口:  http://**************:1985/api/v1/summaries"
    echo ""
    echo "推流地址:"
    echo "  WebRTC: webrtc://**************:8000/live/livestream"
    echo ""
    echo "播放地址:"
    echo "  WebRTC:   webrtc://**************:8000/live/livestream"
    echo "  HTTP-FLV: http://**************:8080/live/livestream.flv"
    echo "  HLS:      http://**************:8080/live/livestream.m3u8"
    echo "  RTMP:     rtmp://**************:1935/live/livestream"
else
    echo -e "${RED}✗ SRS服务未运行，请先启动服务${NC}"
    echo "  启动命令: ./start_srs_webrtc.sh"
fi

echo ""
echo "如需帮助，请查看: SRS_WebRTC_使用指南.md"
