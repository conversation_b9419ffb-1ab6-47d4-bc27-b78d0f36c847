<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebRTC推流测试 - SRS局域网版</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 30px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 20px;
        }
        
        .header h1 {
            color: #333;
            margin: 0;
            font-size: 28px;
        }
        
        .header p {
            color: #666;
            margin: 10px 0 0 0;
            font-size: 14px;
        }
        
        .control-panel {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #333;
        }
        
        .form-group input, .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            box-sizing: border-box;
        }
        
        .form-group input:focus, .form-group select:focus {
            border-color: #007bff;
            outline: none;
        }
        
        .button-group {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }
        
        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s;
            flex: 1;
        }
        
        .btn-primary {
            background-color: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background-color: #0056b3;
        }
        
        .btn-danger {
            background-color: #dc3545;
            color: white;
        }
        
        .btn-danger:hover {
            background-color: #c82333;
        }
        
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        
        .video-panel {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            margin-bottom: 30px;
        }
        
        .video-container {
            text-align: center;
        }
        
        .video-container h3 {
            margin-bottom: 15px;
            color: #333;
        }
        
        video {
            width: 100%;
            max-width: 400px;
            height: 300px;
            background-color: #000;
            border-radius: 8px;
            border: 2px solid #ddd;
        }
        
        .status-panel {
            background-color: #f8f9fa;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .status-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        
        .status-label {
            font-weight: bold;
            color: #495057;
        }
        
        .status-value {
            color: #007bff;
            font-family: monospace;
        }
        
        .status-success {
            color: #28a745;
        }
        
        .status-error {
            color: #dc3545;
        }
        
        .status-warning {
            color: #ffc107;
        }
        
        .info-panel {
            background-color: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 0 4px 4px 0;
        }
        
        .info-panel h4 {
            margin: 0 0 10px 0;
            color: #007bff;
        }
        
        .info-panel ul {
            margin: 0;
            padding-left: 20px;
        }
        
        .info-panel li {
            margin-bottom: 5px;
            color: #333;
        }
        
        @media (max-width: 768px) {
            .control-panel, .video-panel {
                grid-template-columns: 1fr;
            }
            
            .button-group {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>WebRTC推流测试系统</h1>
            <p>SRS 5.0 局域网版本 - 服务器地址: **************</p>
        </div>
        
        <div class="info-panel">
            <h4>使用说明</h4>
            <ul>
                <li>确保SRS服务器已启动（端口8000、1985、8080）</li>
                <li>浏览器需要支持WebRTC（Chrome、Firefox、Safari等）</li>
                <li>首次使用需要授权摄像头和麦克风权限</li>
                <li>推流成功后可通过播放地址观看直播</li>
            </ul>
        </div>
        
        <div class="control-panel">
            <div>
                <div class="form-group">
                    <label for="streamUrl">推流地址</label>
                    <input type="text" id="streamUrl" value="webrtc://**************:8000/live/livestream" readonly>
                </div>
                
                <div class="form-group">
                    <label for="streamName">流名称</label>
                    <input type="text" id="streamName" value="livestream" placeholder="输入流名称">
                </div>
                
                <div class="form-group">
                    <label for="videoQuality">视频质量</label>
                    <select id="videoQuality">
                        <option value="320x240">320x240 (低质量)</option>
                        <option value="640x480" selected>640x480 (标准质量)</option>
                        <option value="1280x720">1280x720 (高质量)</option>
                    </select>
                </div>
                
                <div class="button-group">
                    <button id="startBtn" class="btn btn-primary">开始推流</button>
                    <button id="stopBtn" class="btn btn-danger" disabled>停止推流</button>
                </div>
            </div>
            
            <div class="status-panel">
                <h3>推流状态</h3>
                <div class="status-item">
                    <span class="status-label">连接状态:</span>
                    <span id="connectionStatus" class="status-value">未连接</span>
                </div>
                <div class="status-item">
                    <span class="status-label">会话ID:</span>
                    <span id="sessionId" class="status-value">-</span>
                </div>
                <div class="status-item">
                    <span class="status-label">视频编码:</span>
                    <span id="videoCodec" class="status-value">-</span>
                </div>
                <div class="status-item">
                    <span class="status-label">音频编码:</span>
                    <span id="audioCodec" class="status-value">-</span>
                </div>
                <div class="status-item">
                    <span class="status-label">推流时长:</span>
                    <span id="duration" class="status-value">00:00:00</span>
                </div>
            </div>
        </div>
        
        <div class="video-panel">
            <div class="video-container">
                <h3>本地预览</h3>
                <video id="localVideo" autoplay muted playsinline></video>
            </div>
            
            <div class="video-container">
                <h3>播放地址</h3>
                <div style="text-align: left; font-size: 14px; line-height: 1.6;">
                    <p><strong>WebRTC播放:</strong><br>
                    <code>webrtc://**************:8000/live/<span id="playStreamName">livestream</span></code></p>
                    
                    <p><strong>HTTP-FLV播放:</strong><br>
                    <code>http://**************:8080/live/<span id="playStreamName2">livestream</span>.flv</code></p>
                    
                    <p><strong>HLS播放:</strong><br>
                    <code>http://**************:8080/live/<span id="playStreamName3">livestream</span>.m3u8</code></p>
                    
                    <p><strong>RTMP播放:</strong><br>
                    <code>rtmp://**************:1935/live/<span id="playStreamName4">livestream</span></code></p>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入SRS SDK -->
    <script src="https://**************:8443/players/js/adapter-7.4.0.min.js"></script>
    <script src="https://**************:8443/players/js/srs.sdk.js"></script>

    <script>
        // 全局变量
        let sdk = null;
        let isPublishing = false;
        let startTime = null;
        let durationTimer = null;

        // DOM元素
        const startBtn = document.getElementById('startBtn');
        const stopBtn = document.getElementById('stopBtn');
        const streamUrl = document.getElementById('streamUrl');
        const streamName = document.getElementById('streamName');
        const videoQuality = document.getElementById('videoQuality');
        const localVideo = document.getElementById('localVideo');
        const connectionStatus = document.getElementById('connectionStatus');
        const sessionId = document.getElementById('sessionId');
        const videoCodec = document.getElementById('videoCodec');
        const audioCodec = document.getElementById('audioCodec');
        const duration = document.getElementById('duration');

        // 播放地址元素
        const playStreamNames = [
            document.getElementById('playStreamName'),
            document.getElementById('playStreamName2'),
            document.getElementById('playStreamName3'),
            document.getElementById('playStreamName4')
        ];

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateStreamUrl();
            updatePlayUrls();

            // 事件监听
            startBtn.addEventListener('click', startPublish);
            stopBtn.addEventListener('click', stopPublish);
            streamName.addEventListener('input', function() {
                updateStreamUrl();
                updatePlayUrls();
            });
            videoQuality.addEventListener('change', updateStreamUrl);
        });

        // 更新推流地址
        function updateStreamUrl() {
            const name = streamName.value || 'livestream';
            streamUrl.value = `webrtc://**************:8000/live/${name}`;
        }

        // 更新播放地址
        function updatePlayUrls() {
            const name = streamName.value || 'livestream';
            playStreamNames.forEach(element => {
                if (element) element.textContent = name;
            });
        }

        // 更新状态显示
        function updateStatus(status, className = '') {
            connectionStatus.textContent = status;
            connectionStatus.className = `status-value ${className}`;
        }

        // 格式化时长
        function formatDuration(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
        }

        // 更新推流时长
        function updateDuration() {
            if (startTime) {
                const elapsed = Math.floor((Date.now() - startTime) / 1000);
                duration.textContent = formatDuration(elapsed);
            }
        }

        // 获取视频约束
        function getVideoConstraints() {
            const quality = videoQuality.value;
            const [width, height] = quality.split('x').map(Number);

            return {
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true
                },
                video: {
                    width: { ideal: width, max: width },
                    height: { ideal: height, max: height },
                    frameRate: { ideal: 30, max: 30 }
                }
            };
        }

        // 开始推流
        async function startPublish() {
            try {
                updateStatus('正在连接...', 'status-warning');
                startBtn.disabled = true;

                // 创建SDK实例
                if (sdk) {
                    sdk.close();
                }
                sdk = new SrsRtcPublisherAsync();

                // 设置约束
                sdk.constraints = getVideoConstraints();

                // 设置本地视频流
                localVideo.srcObject = sdk.stream;

                // 监听编解码器信息
                sdk.pc.onicegatheringstatechange = function(event) {
                    if (sdk.pc.iceGatheringState === "complete") {
                        try {
                            const senders = sdk.pc.getSenders();
                            const audioSender = senders.find(s => s.track && s.track.kind === 'audio');
                            const videoSender = senders.find(s => s.track && s.track.kind === 'video');

                            if (audioSender && audioSender.getParameters) {
                                const params = audioSender.getParameters();
                                if (params.codecs && params.codecs.length > 0) {
                                    audioCodec.textContent = params.codecs[0].mimeType || 'opus';
                                }
                            }

                            if (videoSender && videoSender.getParameters) {
                                const params = videoSender.getParameters();
                                if (params.codecs && params.codecs.length > 0) {
                                    videoCodec.textContent = params.codecs[0].mimeType || 'H.264';
                                }
                            }
                        } catch (e) {
                            console.warn('获取编解码器信息失败:', e);
                            audioCodec.textContent = 'opus';
                            videoCodec.textContent = 'H.264';
                        }
                    }
                };

                // 开始推流
                const url = streamUrl.value;
                const session = await sdk.publish(url);

                // 推流成功
                isPublishing = true;
                startTime = Date.now();
                updateStatus('推流中', 'status-success');
                sessionId.textContent = session.sessionid || '已连接';

                // 启动时长计时器
                durationTimer = setInterval(updateDuration, 1000);

                // 更新按钮状态
                startBtn.disabled = true;
                stopBtn.disabled = false;

                console.log('推流成功:', session);

            } catch (error) {
                console.error('推流失败:', error);
                updateStatus('推流失败', 'status-error');

                let errorMsg = '推流失败';
                if (error.name === 'HttpsRequiredError') {
                    errorMsg = 'HTTPS环境要求错误';
                } else if (error.name === 'NotAllowedError') {
                    errorMsg = '用户拒绝摄像头/麦克风权限';
                } else if (error.name === 'NotFoundError') {
                    errorMsg = '未找到摄像头或麦克风设备';
                } else if (error.message) {
                    errorMsg = error.message;
                }

                alert(`推流失败: ${errorMsg}`);

                // 重置状态
                resetState();
            }
        }

        // 停止推流
        function stopPublish() {
            try {
                if (sdk) {
                    sdk.close();
                    sdk = null;
                }

                updateStatus('已停止', 'status-warning');
                resetState();

                console.log('推流已停止');

            } catch (error) {
                console.error('停止推流失败:', error);
            }
        }

        // 重置状态
        function resetState() {
            isPublishing = false;
            startTime = null;

            if (durationTimer) {
                clearInterval(durationTimer);
                durationTimer = null;
            }

            // 重置显示
            sessionId.textContent = '-';
            videoCodec.textContent = '-';
            audioCodec.textContent = '-';
            duration.textContent = '00:00:00';

            // 重置按钮状态
            startBtn.disabled = false;
            stopBtn.disabled = true;

            // 清除本地视频
            if (localVideo.srcObject) {
                const tracks = localVideo.srcObject.getTracks();
                tracks.forEach(track => track.stop());
                localVideo.srcObject = null;
            }
        }

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', function() {
            if (sdk) {
                sdk.close();
            }
        });
    </script>
</body>
</html>
